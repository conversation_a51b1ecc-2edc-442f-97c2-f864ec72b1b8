package xtrabackup

import "testing"

func Test_backupExecCommand(t *testing.T) {
	type args struct {
		options *BackupOptions
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(t *testing.T, err error)
	}{
		{
			name: "test timeout",
			before: func() {
				binXtrabackup = "./xtrabackup"
				logPath = "/home/<USER>/backups"
			},
			args: args{
				options: &BackupOptions{
					Cluster:              "test",
					DefaultFile:          "/home/<USER>/mysql/etc/my.cnf",
					Socket:               "/home/<USER>/mysql/tmp/mysql.sock",
					User:                 "root",
					Password:             "_Y5%C2wncJC6b^frHdiEKw*kn05VNN",
					UseMemory:            "8G",
					CompressThreads:      4,
					Parallel:             16,
					Type:                 "full",
					Throttle:             500,
					KillLongQueryType:    "select",
					KillLongQueryTimeout: 20,
					Stream:               true,
					RemoteHost:           "work@************",
					RemotePath:           "/home/<USER>/backups",
				},
			},
			expect: func(t *testing.T, err error) {
				if err == nil {
					t.<PERSON><PERSON><PERSON>("expected error, got %v", err)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := backupExecCommand(tt.args.options)
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}

}
