package xtrabackup

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"mdc-agent/library/logger"
	"mdc-agent/library/utils"
)

// RestoreOptions 恢复选项
type RestoreOptions struct {
	FullBackupFile   string   // 全量备份文件地址，格式为 mysql@ip:/path/to/backup.xbstream
	IncrementalFiles []string // 增量备份文件列表，格式为 mysql@ip:/path/to/backup.xbstream
	TargetDataDir    string   // 目标Data目录

	UseMemory       string // 使用内存大小
	ParallelThreads int    // 并行线程数

	// 内部使用字段
	tmpDir   string   // 临时工作目录
	fullDir  string   // 全量备份解压目录
	incrDirs []string // 增量备份解压目录列表
}

// RestoreResult 恢复结果
type RestoreResult struct {
	Success   bool          // 是否成功
	StartTime time.Time     // 开始时间
	EndTime   time.Time     // 结束时间
	Duration  time.Duration // 耗时
	DataPath  string        // 数据路径
	ErrorMsg  string        // 错误信息
}

// 1、下载全量备份文件、增量备份文件到临时目录
// 2、xbstream 解压到临时目录
// xbstream -x -C /path/to/full < /path/to/backup.xbstream
// xbstream -x -C /path/to/incr1  < /path/to/incremental-1.xbstream
// xbstream -x -C /path/to/incr2  < /path/to/incremental-2.xbstream

// 2、qpress 解压
// xtrabackup --decompress --remove-original --parallel=8 --target-dir=/path/to/full
// xtrabackup --decompress --remove-original --parallel=8 --target-dir=/path/to/incr1
// xtrabackup --decompress --remove-original --parallel=8 --target-dir=/path/to/incr2
//
// 3、Prepare数据
// xtrabackup --prepare --apply-log-only --target-dir=/path/to/full
// xtrabackup --prepare --apply-log-only --target-dir=/path/to/full --incremental-dir=/path/to/incr1
// xtrabackup --prepare --target-dir=/path/to/full --incremental-dir=/path/to/incr2
//
// 4、Moveback（如果直接将全备文件解压到 datadir 中，就不需要这一步了）
// mv /path/to/full/* /path/to/data
//
// 5、数据表修复
// bin/myisamchk -c -r ./var/mysql/user
// bin/myisamchk -c -r ./var/mysql/db
// bin/myisamchk -c -r ./var/mysql/tables_priv

// 恢复参数校验
func restoreArgsValidate(options *RestoreOptions) error {
	// 必须要有全备文件
	if options.FullBackupFile == "" {
		return fmt.Errorf("full backup file cannot be empty")
	}

	// 目标目录必须指定
	if options.TargetDataDir == "" {
		return fmt.Errorf("target data directory cannot be empty")
	}

	// 参数默认值
	if options.UseMemory == "" {
		options.UseMemory = useMemory
	}
	if options.ParallelThreads <= 0 {
		options.ParallelThreads = 4
	}

	// 创建临时工作目录
	timestamp := time.Now().Format("2006-01-02-15-04-05")
	options.tmpDir = fmt.Sprintf("%s/restore-%s", tmpPath, timestamp)

	return nil
}

// 下载文件，支持 mysql@ip:/path/to/backup.xbstream 格式
func download(fileURL, targetDir string) error {
	// 创建目标目录
	dir := filepath.Dir(targetDir)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create target directory: %w", err)
	}

	// 


	// 使用 wget 下载文件
	cmd := fmt.Sprintf("wget %s %s", fileURL, target)
	logger.Info("Downloading file: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
	})
	if err != nil {
		return fmt.Errorf("download failed: %w, output: %s", err, output)
	}

	logger.Info("File downloaded successfully: %s", target)
	return nil
}

// 解压文件，xbstream + qpress
func decompress(filePath, targetDir string, parallelThreads int) error {
	// 创建目标目录
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return fmt.Errorf("failed to create target directory: %w", err)
	}

	// 1. xbstream 解压
	if strings.HasSuffix(filePath, ".xbstream") {
		cmd := fmt.Sprintf("%s -x -C %s < %s", binXbstream, targetDir, filePath)
		logger.Info("Extracting xbstream: %s", cmd)

		output, err := utils.ExecCommand(&utils.Shell{
			Command: "bash",
			Args:    []string{"-c", cmd},
		})
		if err != nil {
			return fmt.Errorf("xbstream extraction failed: %w, output: %s", err, output)
		}
		logger.Info("Xbstream extraction completed")
	}

	// 2. qpress 解压
	cmd := fmt.Sprintf("%s --decompress --remove-original --parallel=%d --target-dir=%s",
		binXtrabackup, parallelThreads, targetDir)
	logger.Info("Decompressing files: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
	})
	if err != nil {
		return fmt.Errorf("qpress decompression failed: %w, output: %s", err, output)
	}

	logger.Info("File decompression completed")
	return nil
}

// 准备数据
// - 如果只有全备文件， xtrabackup --prepare --target-dir=/path/to/full 即可
// - 如果有多个增量文件，需要检查每个增备文件的xtrabackup_checkpoints，确保start_lsn和previous_lsn一致
func prepare(options *RestoreOptions) error {
	// 1. 准备全量备份
	isOnlyFull := len(options.incrDirs) == 0
	if err := applyLogToBackup(options.fullDir, options.UseMemory, !isOnlyFull); err != nil {
		return fmt.Errorf("failed to prepare full backup: %w", err)
	}

	// 2. 依次应用增量备份
	for i, incrDir := range options.incrDirs {
		isLast := (i == len(options.incrDirs)-1)
		if err := applyIncrementalBackup(options.fullDir, incrDir, options.UseMemory, !isLast); err != nil {
			return fmt.Errorf("failed to apply incremental backup %d: %w", i+1, err)
		}
	}

	return nil
}

// applyLogToBackup 对备份应用日志
func applyLogToBackup(backupPath, useMemory string, applyLogOnly bool) error {
	var cmdParts []string

	// 基础命令
	cmdParts = append(cmdParts, binXtrabackup)

	// 配置文件
	configFile := filepath.Join(backupPath, "backup-my.cnf")
	if _, err := os.Stat(configFile); err == nil {
		cmdParts = append(cmdParts, fmt.Sprintf("--defaults-file=%s", configFile))
	}

	// apply-log 操作
	cmdParts = append(cmdParts, "--prepare")

	// 如果不是最后一个备份，使用 --apply-log-only
	if applyLogOnly {
		cmdParts = append(cmdParts, "--apply-log-only")
	}

	// 内存使用
	if useMemory != "" {
		cmdParts = append(cmdParts, fmt.Sprintf("--use-memory=%s", useMemory))
	}

	// 目标目录
	cmdParts = append(cmdParts, fmt.Sprintf("--target-dir=%s", backupPath))

	cmd := strings.Join(cmdParts, " ")
	logger.Info("Applying log to backup: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
	})
	if err != nil {
		return fmt.Errorf("apply-log failed: %w, output: %s", err, output)
	}

	logger.Info("Apply-log completed successfully")
	return nil
}

// applyIncrementalBackup 应用增量备份
func applyIncrementalBackup(basePath, incrementalPath, useMemory string, applyLogOnly bool) error {
	var cmdParts []string

	// 基础命令
	cmdParts = append(cmdParts, binXtrabackup)

	// 配置文件
	configFile := filepath.Join(incrementalPath, "backup-my.cnf")
	if _, err := os.Stat(configFile); err == nil {
		cmdParts = append(cmdParts, fmt.Sprintf("--defaults-file=%s", configFile))
	}

	// apply-log 操作
	cmdParts = append(cmdParts, "--prepare")

	// 如果不是最后一个增量备份，使用 --apply-log-only
	if applyLogOnly {
		cmdParts = append(cmdParts, "--apply-log-only")
	}

	// 内存使用
	if useMemory != "" {
		cmdParts = append(cmdParts, fmt.Sprintf("--use-memory=%s", useMemory))
	}

	// 增量目录
	cmdParts = append(cmdParts, fmt.Sprintf("--incremental-dir=%s", incrementalPath))

	// 基础目录
	cmdParts = append(cmdParts, fmt.Sprintf("--target-dir=%s", basePath))

	cmd := strings.Join(cmdParts, " ")
	logger.Info("Applying incremental backup: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
	})
	if err != nil {
		return fmt.Errorf("incremental apply-log failed: %w, output: %s", err, output)
	}

	logger.Info("Incremental apply-log completed successfully")
	return nil
}

// MySQL实例恢复
func Restore(options *RestoreOptions) (*RestoreResult, error) {
	result := &RestoreResult{
		StartTime: time.Now(),
	}

	// 1. 参数校验
	if err := restoreArgsValidate(options); err != nil {
		result.Success = false
		result.ErrorMsg = err.Error()
		return result, err
	}

	// 确保清理临时目录
	defer func() {
		if err := os.RemoveAll(options.tmpDir); err != nil {
			logger.Warn("Failed to clean up temporary directory: %v", err)
		}
	}()

	// 2. 下载全量备份文件
	fullBackupLocal := filepath.Join(options.tmpDir, "full-backup.xbstream")
	if err := download(options.FullBackupFile, fullBackupLocal); err != nil {
		result.Success = false
		result.ErrorMsg = err.Error()
		return result, err
	}

	// 3. 下载增量备份文件
	var incrBackupLocals []string
	for i, incrFile := range options.IncrementalFiles {
		incrLocal := filepath.Join(options.tmpDir, fmt.Sprintf("incr-%d-backup.xbstream", i+1))
		if err := download(incrFile, incrLocal); err != nil {
			result.Success = false
			result.ErrorMsg = err.Error()
			return result, err
		}
		incrBackupLocals = append(incrBackupLocals, incrLocal)
	}

	// 4. 解压全量备份
	if err := decompress(fullBackupLocal, options.fullDir, options.ParallelThreads); err != nil {
		result.Success = false
		result.ErrorMsg = err.Error()
		return result, err
	}

	// 5. 解压增量备份
	for i, incrLocal := range incrBackupLocals {
		if err := decompress(incrLocal, options.incrDirs[i], options.ParallelThreads); err != nil {
			result.Success = false
			result.ErrorMsg = err.Error()
			return result, err
		}
	}

	// 6. 数据准备 prepare
	if err := prepare(options); err != nil {
		result.Success = false
		result.ErrorMsg = err.Error()
		return result, err
	}

	// 7. 移动数据到目标目录
	if err := moveDataToTarget(options.fullDir, options.TargetDataDir); err != nil {
		result.Success = false
		result.ErrorMsg = err.Error()
		return result, err
	}

	result.Success = true
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.DataPath = options.TargetDataDir

	logger.Info("MySQL restore completed successfully in %v", result.Duration)
	return result, nil
}

// moveDataToTarget 移动数据到目标目录
func moveDataToTarget(sourceDir, targetDir string) error {
	// 确保目标目录存在
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return fmt.Errorf("failed to create target directory: %w", err)
	}

	// 移动数据文件
	cmd := fmt.Sprintf("mv %s/* %s/", sourceDir, targetDir)
	logger.Info("Moving data to target: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
	})
	if err != nil {
		return fmt.Errorf("failed to move data: %w, output: %s", err, output)
	}

	logger.Info("Data moved to target directory successfully")
	return nil
}
