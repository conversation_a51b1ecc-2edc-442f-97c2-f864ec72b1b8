package xtrabackup

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"mdc-agent/library/logger"
	"mdc-agent/library/utils"
)

// RestoreOptions 恢复选项
type RestoreOptions struct {
	FullBackupFile   string   // 全量备份文件地址，格式为 mysql@ip:/path/to/backup.xbstream
	IncrementalFiles []string // 增量备份文件列表，格式为 mysql@ip:/path/to/backup.xbstream
	TargetDataDir    string   // 目标Data目录

	UseMemory       string // 使用内存大小
	ParallelThreads int    // 并行线程数
}

// 1、下载全量备份文件、增量备份文件到临时目录
// 2、xbstream 解压到临时目录
// xbstream -x -C /path/to/full < /path/to/backup.xbstream
// xbstream -x -C /path/to/incr1  < /path/to/incremental-1.xbstream
// xbstream -x -C /path/to/incr2  < /path/to/incremental-2.xbstream

// 2、qpress 解压
// xtrabackup --decompress --remove-original --parallel=8 --target-dir=/path/to/full
// xtrabackup --decompress --remove-original --parallel=8 --target-dir=/path/to/incr1
// xtrabackup --decompress --remove-original --parallel=8 --target-dir=/path/to/incr2
//
// 3、Prepare数据
// xtrabackup --prepare --apply-log-only --target-dir=/path/to/full
// xtrabackup --prepare --apply-log-only --target-dir=/path/to/full --incremental-dir=/path/to/incr1
// xtrabackup --prepare --target-dir=/path/to/full --incremental-dir=/path/to/incr2
//
// 4、Moveback（如果直接将全备文件解压到 datadir 中，就不需要这一步了）
// mv /path/to/full/* /path/to/data
//
// 5、数据表修复
// bin/myisamchk -c -r ./var/mysql/user
// bin/myisamchk -c -r ./var/mysql/db
// bin/myisamchk -c -r ./var/mysql/tables_priv

// 恢复参数校验
func restoreArgsValidate(options *RestoreOptions) error {
	// 必须要有全备文件

	// 参数默认值

	return nil
}

// 下载文件
func download(source, target string) error {

	return nil
}

// 解压文件，xbstream + qpress
func decompress(filePath, targetDir string) error {
	// 检查后缀，使用xbstream解压到targetDir

	// qpress 解压

	return nil
}

// 准备数据
// - 如果只有全备文件， xtrabackup --prepare --target-dir=/path/to/full 即可
// - 如果有多个增量文件，需要检查每个增备文件的xtrabackup_checkpoints，确保start_lsn和previous_lsn一致
func prepare(options *RestoreOptions) error {

	return nil
}

// MySQL实例恢复
func Restore(options *RestoreOptions) error {
	// 参数校验

	// 备份文件下载

	// 解压缩

	// 数据准备 prepare

	return nil
}

//
//
//

// XtraBackupManager XtraBackup 管理器实现
type XtraBackupManager struct {
	config         *Config
	limiter        *RateLimiter
	monitor        *ProgressMonitor
	processManager *ProcessManager
	// replicationMgr *ReplicationManager
	status    BackupStatus
	mutex     sync.RWMutex
	pauseFile string
	stopChan  chan struct{}
}

// NewManager 创建新的 XtraBackup 管理器
func NewManager(config *Config) *XtraBackupManager {
	return &XtraBackupManager{
		config: config,
		// limiter: NewRateLimiter(config.Throttle),
		// monitor:        NewProgressMonitor(),
		processManager: NewProcessManager(),
		// replicationMgr: NewReplicationManager(config),
		status:    StatusIdle,
		pauseFile: "/tmp/xtrabackup_pause",
		stopChan:  make(chan struct{}),
	}
}

// PrepareBackup 准备备份（apply-log）
func (m *XtraBackupManager) PrepareBackup(ctx context.Context, opts *RestoreOptions) (*RestoreResult, error) {
	result := &RestoreResult{
		StartTime: time.Now(),
	}

	// 1. 准备全量备份
	if err := m.applyLogToBackup(opts.BackupPath, true, len(opts.IncrementalDirs) == 0); err != nil {
		result.Success = false
		result.ErrorMsg = err.Error()
		return result, err
	}

	// 2. 依次应用增量备份
	for i, incDir := range opts.IncrementalDirs {
		isLast := (i == len(opts.IncrementalDirs)-1)
		if err := m.applyIncrementalBackup(opts.BackupPath, incDir, isLast); err != nil {
			result.Success = false
			result.ErrorMsg = err.Error()
			return result, err
		}
	}

	result.Success = true
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.DataPath = opts.BackupPath

	return result, nil
}

// RestoreBackup 恢复备份（move-back）
func (m *XtraBackupManager) RestoreBackup(ctx context.Context, opts *RestoreOptions) (*RestoreResult, error) {
	result := &RestoreResult{
		StartTime: time.Now(),
	}

	// 1. 修改配置文件
	if err := m.modifyBackupConfig(opts.BackupPath, opts.TargetDir); err != nil {
		result.Success = false
		result.ErrorMsg = err.Error()
		return result, err
	}

	// 2. 执行 move-back
	if err := m.moveBackData(opts.BackupPath, opts.TargetDir); err != nil {
		result.Success = false
		result.ErrorMsg = err.Error()
		return result, err
	}

	result.Success = true
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.DataPath = opts.TargetDir

	return result, nil
}

// applyLogToBackup 对备份应用日志
func (m *XtraBackupManager) applyLogToBackup(backupPath string, _, isLast bool) error {
	var cmdParts []string

	// 基础命令
	cmdParts = append(cmdParts, m.config.BinPath)

	// 配置文件
	configFile := filepath.Join(backupPath, "backup-my.cnf")
	cmdParts = append(cmdParts, fmt.Sprintf("--defaults-file=%s", configFile))

	// apply-log 操作
	cmdParts = append(cmdParts, "--apply-log")

	// 如果不是最后一个备份，使用 --redo-only
	if !isLast {
		cmdParts = append(cmdParts, "--redo-only")
	}

	// 内存使用
	if m.config.UseMemory != "" {
		cmdParts = append(cmdParts, fmt.Sprintf("--use-memory=%s", m.config.UseMemory))
	}

	// 目标目录
	cmdParts = append(cmdParts, backupPath)

	cmd := strings.Join(cmdParts, " ")
	logger.Info("Applying log to backup: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
		// Timeout: 5 * time.Hour,
	})
	if err != nil {
		return fmt.Errorf("apply-log failed: %w, output: %s", err, output)
	}

	logger.Info("Apply-log completed successfully")
	return nil
}

// applyIncrementalBackup 应用增量备份
func (m *XtraBackupManager) applyIncrementalBackup(basePath, incrementalPath string, isLast bool) error {
	var cmdParts []string

	// 基础命令
	cmdParts = append(cmdParts, m.config.BinPath)

	// 配置文件
	configFile := filepath.Join(incrementalPath, "backup-my.cnf")
	cmdParts = append(cmdParts, fmt.Sprintf("--defaults-file=%s", configFile))

	// apply-log 操作
	cmdParts = append(cmdParts, "--apply-log")

	// 如果不是最后一个增量备份，使用 --redo-only
	if !isLast {
		cmdParts = append(cmdParts, "--redo-only")
	}

	// 内存使用
	if m.config.UseMemory != "" {
		cmdParts = append(cmdParts, fmt.Sprintf("--use-memory=%s", m.config.UseMemory))
	}

	// 增量目录
	cmdParts = append(cmdParts, fmt.Sprintf("--incremental-dir=%s", incrementalPath))

	// 基础目录
	cmdParts = append(cmdParts, basePath)

	cmd := strings.Join(cmdParts, " ")
	logger.Info("Applying incremental backup: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
		// Timeout: 5 * time.Hour,
	})
	if err != nil {
		return fmt.Errorf("incremental apply-log failed: %w, output: %s", err, output)
	}

	logger.Info("Incremental apply-log completed successfully")
	return nil
}

// modifyBackupConfig 修改备份配置文件
func (m *XtraBackupManager) modifyBackupConfig(backupPath, targetDir string) error {
	configFile := filepath.Join(backupPath, "backup-my.cnf")

	// 删除 innodb_undo_directory 配置
	removeUndoCmd := fmt.Sprintf("sed -i '/innodb_undo_directory=/d' %s", configFile)
	if _, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", removeUndoCmd},
		// Timeout: 5 * time.Hour,
	}); err != nil {
		return fmt.Errorf("failed to remove innodb_undo_directory: %w", err)
	}

	// 添加 datadir 配置
	addDatadirCmd := fmt.Sprintf("sed -i '$adatadir=%s/var' %s", targetDir, configFile)
	if _, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", addDatadirCmd},
		// Timeout: 5 * time.Hour,
	}); err != nil {
		return fmt.Errorf("failed to add datadir: %w", err)
	}

	logger.Info("Backup configuration modified successfully")
	return nil
}

// moveBackData 执行 move-back 操作
func (m *XtraBackupManager) moveBackData(backupPath, _ string) error {
	var cmdParts []string

	// 基础命令
	cmdParts = append(cmdParts, m.config.BinPath)

	// 配置文件
	configFile := filepath.Join(backupPath, "backup-my.cnf")
	cmdParts = append(cmdParts, fmt.Sprintf("--defaults-file=%s", configFile))

	// move-back 操作
	cmdParts = append(cmdParts, "--move-back")

	// 源目录
	cmdParts = append(cmdParts, backupPath)

	cmd := strings.Join(cmdParts, " ")
	logger.Info("Executing move-back: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
		// Timeout: 5 * time.Hour,
	})
	if err != nil {
		return fmt.Errorf("move-back failed: %w, output: %s", err, output)
	}

	// move-back 完成后删除源目录
	if err := os.RemoveAll(backupPath); err != nil {
		logger.Warn("Failed to remove backup directory after move-back: %v", err)
	}

	logger.Info("Move-back completed successfully")
	return nil
}

// DecompressBackup 解压备份文件
func (m *XtraBackupManager) DecompressBackup(backupPath string, parallelThreads int) error {
	// // 检查是否为 xbstream 格式
	// if strings.Contains(backupPath, "xbstream") {
	// 	if err := m.extractXbstream(backupPath); err != nil {
	// 		return fmt.Errorf("failed to extract xbstream: %w", err)
	// 	}
	// }

	// // 解压缩文件
	// if err := m.decompressFiles(backupPath, parallelThreads); err != nil {
	// 	return fmt.Errorf("failed to decompress files: %w", err)
	// }

	return nil
}

// // 构建解压命令
// cmd := fmt.Sprintf("export PATH=$PATH:%s && %s --decompress --remove-original --parallel=%d %s/",
// 	filepath.Dir(m.config.BinPath), m.config.BinPath, parallelThreads, backupPath)

// logger.Info("Decompressing files: %s", cmd)

// output, err := utils.ExecCommand(&utils.Shell{
// 	Command: "bash",
// 	Args:    []string{"-c", cmd},
// 	// Timeout: 5 * time.Hour,
// })
// if err != nil {
// 	return fmt.Errorf("decompression failed: %w, output: %s", err, output)
// }

// logger.Info("File decompression completed successfully")

// extractXbstream 解压 xbstream 文件
// func (m *XtraBackupManager) extractXbstream(xbstreamFile string) error {
// 	// 获取目标目录
// 	targetDir := strings.TrimSuffix(xbstreamFile, ".xbstream")

// 	// 创建目标目录
// 	if err := os.MkdirAll(targetDir, 0755); err != nil {
// 		return fmt.Errorf("failed to create target directory: %w", err)
// 	}

// 	// 解压 xbstream
// 	cmd := fmt.Sprintf("%s -x < %s", m.config.XbstreamPath, xbstreamFile)

// 	// 切换到目标目录执行
// 	oldDir, err := os.Getwd()
// 	if err != nil {
// 		return fmt.Errorf("failed to get current directory: %w", err)
// 	}

// 	if err := os.Chdir(targetDir); err != nil {
// 		return fmt.Errorf("failed to change directory: %w", err)
// 	}
// 	defer os.Chdir(oldDir)

// 	output, err := utils.ExecCommand(&utils.Shell{
// 		Command: "bash",
// 		Args:    []string{"-c", cmd},
// 	})
// 	if err != nil {
// 		return fmt.Errorf("xbstream extraction failed: %w, output: %s", err, output)
// 	}

// 	logger.Info("Xbstream extraction completed successfully")
// 	return nil
// }

// ValidateBackup 验证备份完整性
// func (m *XtraBackupManager) ValidateBackup(backupPath string) error {
// 	// 检查必要的文件是否存在
// 	requiredFiles := []string{
// 		"xtrabackup_info",
// 		"xtrabackup_checkpoints",
// 		"backup-my.cnf",
// 	}

// 	for _, file := range requiredFiles {
// 		filePath := filepath.Join(backupPath, file)
// 		if _, err := os.Stat(filePath); os.IsNotExist(err) {
// 			return fmt.Errorf("required file missing: %s", file)
// 		}
// 	}

// 	// 解析并验证 xtrabackup_info
// 	infoFile := filepath.Join(backupPath, "xtrabackup_info")
// 	xtraInfo, err := ParseXtraInfo(infoFile)
// 	if err != nil {
// 		return fmt.Errorf("failed to validate xtrabackup_info: %w", err)
// 	}

// 	// 验证 LSN 信息
// 	if xtraInfo.LSNFrom == "" || xtraInfo.LSNTo == "" {
// 		return fmt.Errorf("invalid LSN information in backup")
// 	}

// 	logger.Info("Backup validation completed successfully")
// 	return nil
// }

// // GetBackupInfo 获取备份信息
// func (m *XtraBackupManager) GetBackupInfo(backupPath string) (*XtraInfo, error) {
// 	infoFile := filepath.Join(backupPath, "xtrabackup_info")
// 	return ParseXtraInfo(infoFile)
// }
