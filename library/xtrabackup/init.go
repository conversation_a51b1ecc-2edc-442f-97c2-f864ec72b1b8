package xtrabackup

import "fmt"

var (
	binXtrabackup string
	binXbstream   string
	binQpress     string
	tmpPath       string
	logPath       string
	useMemory     string
)

// Config XtraBackup 配置
type Config struct {
	BinPath   string `yaml:"bin_path"`   // xtrabackup 二进制路径
	TmpPath   string `yaml:"tmp_path"`   // 临时文件存放路径
	LogPath   string `yaml:"log_path"`   // 备份日志存放路径
	UseMemory string `yaml:"use_memory"` // 使用内存限制
}

// 初始化相关配置
func Init(cfg *Config) error {
	if cfg.BinPath == "" {
		return fmt.Errorf("xtrabackup bin path can not be empty")
	}
	if cfg.LogPath == "" {
		return fmt.Errorf("xtrabackup log path can not be empty")
	}
	if cfg.UseMemory == "" {
		return fmt.Errorf("xtrabackup use memory can not be empty")
	}

	binXtrabackup = cfg.BinPath + "/xtrabackup"
	binXbstream = cfg.BinPath + "/xbstream"
	binQpress = cfg.BinPath + "/qpress"
	logPath = cfg.LogPath
	useMemory = cfg.UseMemory

	return nil
}
