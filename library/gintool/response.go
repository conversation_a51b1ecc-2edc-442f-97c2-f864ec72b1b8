package gintool

import (
	"math"
	"net/http"

	"github.com/gin-gonic/gin"

	"mdc-agent/library/errs"
)

// 分页结构
type Paging struct {
	Count     int         `json:"count"`
	Page      int         `json:"page"`
	PageSize  int         `json:"pageSize"`
	Rows      interface{} `json:"rows"`
	TotalPage int         `json:"totalPage"`
}

// 分页返回格式化
func FormatPaging(count int, rows interface{}, page int, pageSize int) *Paging {
	totalPage := math.Ceil(float64(count) / float64(pageSize))
	return &Paging{
		Count:     count,
		Page:      page,
		PageSize:  pageSize,
		Rows:      rows,
		TotalPage: int(totalPage),
	}
}

// response body 返回值格式
type body struct {
	// ReqID   string      `json:"reqId"`
	Code    errs.Code   `json:"code"`
	Message string      `json:"msg,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// 返回结果
func JSON(c *gin.Context, data interface{}, err error) {
	var b body
	// b.ReqID = c.MustGet("ReqID").(string)

	if err != nil {
		switch e := err.(type) {
		case *errs.HttpError:
			b.Code = e.Code
			b.Message = e.Error()
			c.Writer.WriteHeader(e.Status)
		case errs.Code:
			b.Code = e
			b.Message = e.Error()
			c.Writer.WriteHeader(e.Status())
		default:
			b.Code = errs.CodeUnknown
			b.Message = e.Error()
			c.Writer.WriteHeader(http.StatusInternalServerError)
		}
	} else if data == nil {
		b.Code = errs.CodeVoidReturn
		b.Message = "no return value"
		c.Writer.WriteHeader(http.StatusInternalServerError)
	} else {
		b.Code = errs.Success
		b.Data = data
	}

	c.JSON(http.StatusOK, b)
	c.Abort()
}

// 适用于与前端对接
type body2fe struct {
	Code    int         `json:"code"`
	Message string      `json:"errMsg,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// 适用于与前端对接的返回值格式化方法
func JSON2FE(c *gin.Context, data interface{}, err error) {
	var b body2fe
	// b.ReqID = c.MustGet("ReqID").(string)

	if err != nil {
		switch e := err.(type) {
		case *errs.HttpError:
			b.Code = e.Code.Int()
			b.Message = e.Error()
			c.Writer.WriteHeader(e.Status)
		case errs.Code:
			b.Code = e.Int()
			b.Message = e.Error()
			c.Writer.WriteHeader(e.Status())
		default:
			b.Code = 1
			b.Message = e.Error()
			c.Writer.WriteHeader(http.StatusInternalServerError)
		}
	} else if data == nil {
		b.Code = errs.CodeVoidReturn.Int()
		b.Message = "no return value"
		c.Writer.WriteHeader(http.StatusInternalServerError)
	} else {
		b.Code = 0
		b.Data = data
	}

	c.JSON(http.StatusOK, b)
	c.Abort()
}
