package gintool

import (
	"github.com/gin-gonic/gin"

	"mdc-agent/library/errs"
)

// 适用于gin的鉴权中间件
func AuthFilter() gin.HandlerFunc {
	urlDoNotNeedAuth := map[string]bool{
		"/auth-center/ui/auth/login": true,
		"/ui/platform/auth/login":    true,
		"/v1/ping":                   true,
	}

	return func(c *gin.Context) {
		if _, exist := urlDoNotNeedAuth[c.Request.RequestURI]; exist {
			return
		}

		// logger.Debug("start auth request")
		token := c.Request.Header.Get("Authorization")
		if token == "" {
			// logger.Debug("request token is not exists")
			JSON2FE(c, nil, errs.New(errs.CodeMissingToken))
			return
		}

		// user, err := authc.ValidateToken(token)
		// if err != nil {
		// 	// logger.Debug("request token:%s is not validate, url: %s", token, c.Request.RequestURI)
		// 	JSON2FE(c, nil, errs.New(errs.CodeAuthenticationFailed))
		// 	return
		// }

		// c.Set("user", user)
	}
}

// 获取用户Info
// func GetUser(c *gin.Context) (*authc.User, error) {
// 	userAny, ok := c.Get("user")
// 	if !ok {
// 		return nil, fmt.Errorf("can't get user")
// 	}

// 	switch user := userAny.(type) {
// 	case *authc.User:
// 		return user, nil
// 	default:
// 		return nil, fmt.Errorf("can't assert user")
// 	}
// }
