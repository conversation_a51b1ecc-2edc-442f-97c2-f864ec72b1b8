package router

import (
	"net/http"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	"mdc-agent/library/errs"
	"mdc-agent/library/gintool"
)

// 适用于gin的鉴权中间件
func AuthFilter(token string) gin.HandlerFunc {
	return func(c *gin.Context) {
		t := c.Request.Header.Get("Authorization")
		if t == "" || t != token {
			gintool.JSON(c, nil, errs.CodeAuthenticationFailed)
			return
		}
	}
}

type Config struct {
	Mode  string `yaml:"mode"`
	Port  int    `yaml:"port"`
	Token string `yaml:"token"`
}

func HealthCheck(c *gin.Context) {
	gintool.JSON(c, "ok", nil)
}

// 404处理
func HandlerNotFound(c *gin.Context) {
	gintool.JSON(c, nil, errs.CodeNotFound.Detail())
}

// 初始化路由
func Init(mode string, token string) *gin.Engine {
	gin.SetMode(mode)

	// init engine
	r := gin.New()
	r.Use(cors.Default())
	r.Use(gintool.Logger)
	r.Use(gintool.Recovery)

	// 404
	r.NoMethod(HandlerNotFound)
	r.NoRoute(HandlerNotFound)
	// HealthCheck
	r.GET("/healthCheck", HealthCheck)

	// 鉴权
	r.Use(AuthFilter(token))

	// 静态文件服务，用于下载备份地址
	r.StaticFS("/home/<USER>/backups", http.Dir("/home/<USER>/backups"))

	return r
}
